import deepdoctection as dd
import json
import os
from datetime import datetime

# Configuration
DEBUG_MODE = False  # Set to False for production mode (no visualization)
# DEBUG_MODE = True:  Shows interactive visualization + detailed console output
# DEBUG_MODE = False: Fast processing, only shows progress + saves JSON data

analyzer = dd.get_dd_analyzer()

path ="/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/inputs/bid_1.pdf"
# path ="/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/inputs/book1_1.pdf"

df = analyzer.analyze(path=path)
df.reset_state()

# Initialize data structure to store all OCR information
document_data = {
    "metadata": {
        "source_file": path,
        "extraction_timestamp": datetime.now().isoformat(),
        "total_pages": 0,
        "analyzer": "deepdoctection"
    },
    "pages": []
}

# Process all pages in the PDF
for page_num, page in enumerate(df, 1):
    if DEBUG_MODE:
        print(f"=== PAGE {page_num} ===")
        print(f"""height: {page.height}
         width: {page.width}
         file_name: {page.file_name}
         document_id: {page.document_id}
         image_id: {page.image_id}
""")
    else:
        print(f"Processing page {page_num}...")
    
    # Extract page data
    page_data = {
        "page_number": page_num,
        "dimensions": {
            "height": page.height,
            "width": page.width
        },
        "file_name": page.file_name,
        "document_id": page.document_id,
        "image_id": page.image_id,
        "layouts": [],
        "tables": []
    }
    
    # Extract layout information
    for layout in page.layouts:
        layout_data = {
            "category_name": layout.category_name,
            "score": float(layout.score) if layout.score else None,
            "reading_order": layout.reading_order,
            "bounding_box": {
                "x1": layout.bounding_box.ulx,
                "y1": layout.bounding_box.uly,
                "x2": layout.bounding_box.lrx,
                "y2": layout.bounding_box.lry,
                "width": layout.bounding_box.width,
                "height": layout.bounding_box.height
            },
            "annotation_id": layout.annotation_id,
            "text": layout.text
        }
        page_data["layouts"].append(layout_data)
    
    # Extract table information if available
    for table in page.tables:
        table_data = {
            "bounding_box": {
                "x1": table.bounding_box.ulx,
                "y1": table.bounding_box.uly,
                "x2": table.bounding_box.lrx,
                "y2": table.bounding_box.lry,
                "width": table.bounding_box.width,
                "height": table.bounding_box.height
            },
            "confidence": float(table.score) if table.score else None,
            "cells": []
        }
        
        # Extract table cells if available
        if hasattr(table, 'cells'):
            for cell in table.cells:
                cell_data = {
                    "text": cell.text if hasattr(cell, 'text') else "",
                    "row": cell.row_number if hasattr(cell, 'row_number') else None,
                    "column": cell.column_number if hasattr(cell, 'column_number') else None,
                    "bounding_box": {
                        "x1": cell.bounding_box.ulx,
                        "y1": cell.bounding_box.uly,
                        "x2": cell.bounding_box.lrx,
                        "y2": cell.bounding_box.lry,
                        "width": cell.bounding_box.width,
                        "height": cell.bounding_box.height
                    }
                }
                table_data["cells"].append(cell_data)
        
        page_data["tables"].append(table_data)
    
    document_data["pages"].append(page_data)
    
    # Show visualization only in debug mode
    if DEBUG_MODE:
        # Visualize the current page
        page.viz(interactive=True,
                 show_tables=True,
                 show_layouts=True,
                 show_figures=True,
                 show_residual_layouts=True)
        
        print(f"=== PAGE {page_num} LAYOUTS ===")
        for layout in page.layouts:
            print(f"""Layout segment: {layout.category_name}, \n 
                    score: {layout.score}, \n 
                    reading_order: {layout.reading_order}, \n
                    bounding_box: {layout.bounding_box}, \n 
                    annotation_id: {layout.annotation_id} \n \n 
                    text: {layout.text} \n""")
        
        print(f"--- End of Page {page_num} ---\n")

# Update total pages count
document_data["metadata"]["total_pages"] = len(document_data["pages"])

# Save to JSON file
output_filename = f"ocr_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
output_path = os.path.join(os.path.dirname(path), "..", "outputs", output_filename)

# Ensure output directory exists
os.makedirs(os.path.dirname(output_path), exist_ok=True)

# Save the data
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(document_data, f, indent=2, ensure_ascii=False)

mode_text = "Debug mode (with visualization)" if DEBUG_MODE else "Production mode (no visualization)"
print(f"\n🎉 OCR extraction completed in {mode_text}")
print(f"💾 Data saved to: {output_path}")
print(f"📊 Extracted data summary:")
print(f"   - Total pages: {document_data['metadata']['total_pages']}")
print(f"   - Total layouts: {sum(len(page['layouts']) for page in document_data['pages'])}")
print(f"   - Total tables: {sum(len(page['tables']) for page in document_data['pages'])}")