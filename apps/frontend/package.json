{"name": "frontend", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "format": "biome format", "lint": "biome lint", "check": "biome check"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-core": "^0.12.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "@tanstack/router-plugin": "^1.121.2", "@xyflow/react": "^12.8.2", "@yoopta/action-menu-list": "^4.9.9", "@yoopta/blockquote": "^4.9.9", "@yoopta/callout": "^4.9.9", "@yoopta/code": "^4.9.9", "@yoopta/divider": "^4.9.9", "@yoopta/editor": "^4.9.9", "@yoopta/headings": "^4.9.9", "@yoopta/image": "^4.9.9", "@yoopta/link-tool": "^4.9.9", "@yoopta/lists": "^4.9.9", "@yoopta/marks": "^4.9.9", "@yoopta/paragraph": "^4.9.9", "@yoopta/table": "^4.9.9", "@yoopta/toolbar": "^4.9.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.476.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-pdf": "^10.1.0", "slate": "^0.118.0", "slate-react": "^0.117.4", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.6", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.3.5", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}