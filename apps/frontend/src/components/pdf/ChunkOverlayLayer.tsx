// Chunk overlay layer for visualizing OCR bounding boxes on PDF pages

import type React from 'react';
import { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { CoordinateMapper } from '../../utils/coordinateMapper';
import type { DocumentChunk, ChunkOverlay, ChunkInteractionEvent } from '../../types/document';

interface ChunkOverlayLayerProps {
  chunks: DocumentChunk[];
  pageNumber: number;
  pageRef: React.RefObject<HTMLDivElement>;
  canvasRef: React.RefObject<HTMLCanvasElement>;
  scale: number;
  selectedChunkIds: Set<string>;
  highlightedChunkIds: Set<string>;
  comparisonChunks?: Map<string, { similarity: number; riskLevel: string }>;
  onChunkInteraction?: (event: ChunkInteractionEvent) => void;
  showLabels?: boolean;
  className?: string;
}

const CHUNK_TYPE_STYLES: Record<string, { borderColor: string; bgColor: string; hoverBgColor: string }> = {
  text: {
    borderColor: 'rgb(59 130 246)', // blue-500
    bgColor: 'rgba(59, 130, 246, 0.1)',
    hoverBgColor: 'rgba(59, 130, 246, 0.2)'
  },
  title: {
    borderColor: 'rgb(239 68 68)', // red-500
    bgColor: 'rgba(239, 68, 68, 0.1)',
    hoverBgColor: 'rgba(239, 68, 68, 0.2)'
  },
  table: {
    borderColor: 'rgb(34 197 94)', // green-500
    bgColor: 'rgba(34, 197, 94, 0.1)',
    hoverBgColor: 'rgba(34, 197, 94, 0.2)'
  },
  image: {
    borderColor: 'rgb(168 85 247)', // purple-500
    bgColor: 'rgba(168, 85, 247, 0.1)',
    hoverBgColor: 'rgba(168, 85, 247, 0.2)'
  },
  list: {
    borderColor: 'rgb(251 146 60)', // orange-500
    bgColor: 'rgba(251, 146, 60, 0.1)',
    hoverBgColor: 'rgba(251, 146, 60, 0.2)'
  }
};

export function ChunkOverlayLayer({
  chunks,
  pageNumber,
  pageRef,
  canvasRef,
  scale,
  selectedChunkIds,
  highlightedChunkIds,
  comparisonChunks,
  onChunkInteraction,
  showLabels = false,
  className = ''
}: ChunkOverlayLayerProps) {
  const overlayRef = useRef<HTMLDivElement>(null);

  // Filter chunks for this specific page
  const pageChunks = useMemo(() => {
    return chunks.filter(chunk => chunk.page_number === pageNumber);
  }, [chunks, pageNumber]);

  // Calculate overlay positions using canvas reference
  const overlays = useMemo(() => {
    if (!canvasRef?.current || !pageRef?.current || pageChunks.length === 0) return [];

    const canvas = canvasRef.current;
    const pageContainer = pageRef.current;

    // Get the canvas and page container positions
    const canvasRect = canvas.getBoundingClientRect();
    const pageRect = pageContainer.getBoundingClientRect();

    // Calculate offset of canvas within page container
    const canvasOffsetX = canvasRect.left - pageRect.left;
    const canvasOffsetY = canvasRect.top - pageRect.top;

    // Use the canvas displayed dimensions
    const canvasDisplayWidth = canvasRect.width;
    const canvasDisplayHeight = canvasRect.height;

    // Calculate PDF page dimensions (canvas internal size divided by device pixel ratio)
    const pdfPageWidth = canvas.width / (scale * window.devicePixelRatio);
    const pdfPageHeight = canvas.height / (scale * window.devicePixelRatio);

    console.log('Canvas debug:', {
      pageNumber,
      canvasRect,
      pageRect,
      canvasOffsetX,
      canvasOffsetY,
      canvasDisplayWidth,
      canvasDisplayHeight,
      canvasInternalWidth: canvas.width,
      canvasInternalHeight: canvas.height,
      pdfPageWidth,
      pdfPageHeight,
      scale,
      devicePixelRatio: window.devicePixelRatio,
      chunksCount: pageChunks.length
    });

    return pageChunks.map(chunk => {
      // Convert PDF coordinates to display coordinates
      const displayCoords = CoordinateMapper.pdfToDisplay(
        chunk.bounding_box,
        pdfPageWidth,
        pdfPageHeight,
        scale
      );

      // Adjust coordinates to account for canvas position within page container
      const adjustedCoords = {
        x: displayCoords.x + canvasOffsetX,
        y: displayCoords.y + canvasOffsetY,
        width: displayCoords.width,
        height: displayCoords.height
      };

      console.log('Chunk positioning:', {
        chunkId: chunk.id,
        boundingBox: chunk.bounding_box,
        displayCoords,
        adjustedCoords
      });

      return {
        chunk,
        displayCoords: adjustedCoords,
        isHighlighted: highlightedChunkIds.has(chunk.id),
        isSelected: selectedChunkIds.has(chunk.id)
      };
    });
  }, [pageChunks, canvasRef, pageRef, scale, selectedChunkIds, highlightedChunkIds, pageNumber]);

  // Position overlay container to cover the entire page
  useEffect(() => {
    if (!pageRef?.current || !overlayRef?.current) return;


    const overlay = overlayRef.current;

    // Position overlay to cover the entire page container
    overlay.style.position = 'absolute';
    overlay.style.left = '0px';
    overlay.style.top = '0px';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.pointerEvents = 'none'; // Container doesn't intercept events
    overlay.style.zIndex = '10';

    console.log('Overlay container positioned for page:', pageNumber);
  }, [pageRef, pageNumber]);

  const handleChunkClick = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;

    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'click'
    });
  }, [onChunkInteraction, overlays]);

  const handleChunkKeyDown = useCallback((chunk: DocumentChunk, event: React.KeyboardEvent) => {
    if (!onChunkInteraction) return;

    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'click'
    });
  }, [onChunkInteraction, overlays]);

  const handleChunkHover = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;
    
    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'hover'
    });
  }, [onChunkInteraction, overlays]);

  if (overlays.length === 0) return null;

  return (
    <div
      ref={overlayRef}
      className={`chunk-overlay-layer ${className}`}
    >
      {overlays.map(({ chunk, displayCoords, isHighlighted, isSelected }) => {
        const style = CHUNK_TYPE_STYLES[chunk.chunk_type] || CHUNK_TYPE_STYLES.text;
        const comparisonData = comparisonChunks?.get(chunk.id);

        return (
          <div
            key={chunk.id}
            className={`absolute pointer-events-auto cursor-pointer transition-all duration-200 ${
              isSelected ? 'ring-2 ring-blue-500' : ''
            } ${isHighlighted ? 'ring-2 ring-yellow-400' : ''}`}
            style={{
              position: 'absolute',
              left: `${displayCoords.x}px`,
              top: `${displayCoords.y}px`,
              width: `${displayCoords.width}px`,
              height: `${displayCoords.height}px`,
              pointerEvents: 'auto',
              backgroundColor: isSelected
                ? 'rgba(59, 130, 246, 0.3)'
                : isHighlighted
                ? 'rgba(251, 191, 36, 0.3)'
                : style.bgColor,
              border: `1px solid ${
                isSelected
                  ? 'rgb(59, 130, 246)'
                  : isHighlighted
                  ? 'rgb(251, 191, 36)'
                  : style.borderColor
              }`
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleChunkClick(chunk, e);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                e.stopPropagation();
                handleChunkKeyDown(chunk, e);
              }
            }}
            onMouseEnter={(e) => {
              handleChunkHover(chunk, e);
            }}
            tabIndex={0}
            role="button"
            aria-label={`${chunk.chunk_type} chunk: ${chunk.content?.substring(0, 50) || ''}...`}
            title={`${chunk.chunk_type}: ${chunk.content?.substring(0, 100) || ''}...`}
          >
            {showLabels && (
              <div className="absolute -top-6 left-0 bg-black text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                {chunk.chunk_type}
                {comparisonData && (
                  <span className="ml-1 text-yellow-300">
                    ({Math.round(comparisonData.similarity * 100)}%)
                  </span>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

// Utility hook for managing chunk overlays
export function useChunkOverlays(
  chunks: DocumentChunk[],
  initialSelection: Set<string> = new Set()
) {
  const [selectedChunks, setSelectedChunks] = useState<Set<string>>(initialSelection);
  const [highlightedChunks, setHighlightedChunks] = useState<Set<string>>(new Set());

  const selectChunk = useCallback((chunkId: string, multi = false) => {
    setSelectedChunks(prev => {
      const newSelection = new Set(multi ? prev : []);
      if (prev.has(chunkId)) {
        newSelection.delete(chunkId);
      } else {
        newSelection.add(chunkId);
      }
      return newSelection;
    });
  }, []);

  const highlightChunk = useCallback((chunkId: string) => {
    setHighlightedChunks(prev => {
      const newHighlights = new Set(prev);
      if (prev.has(chunkId)) {
        newHighlights.delete(chunkId);
      } else {
        newHighlights.add(chunkId);
      }
      return newHighlights;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedChunks(new Set());
  }, []);

  const clearHighlights = useCallback(() => {
    setHighlightedChunks(new Set());
  }, []);

  const overlays = useMemo(() => {
    return chunks.map(chunk => ({
      chunk,
      displayCoords: { x: 0, y: 0, width: 0, height: 0 },
      isHighlighted: highlightedChunks.has(chunk.id),
      isSelected: selectedChunks.has(chunk.id)
    }));
  }, [chunks, selectedChunks, highlightedChunks]);

  return {
    overlays,
    selectedChunks,
    highlightedChunks,
    selectChunk,
    highlightChunk,
    clearSelection,
    clearHighlights
  };
}
