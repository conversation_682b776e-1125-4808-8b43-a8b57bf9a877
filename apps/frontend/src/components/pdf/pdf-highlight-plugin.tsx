import type { Plugin, PluginRenderPageLayer } from '@react-pdf-viewer/core';
import { Check, Copy, Info, Languages, MessageCircleQuestion, Search } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { type OCRHighlight } from './document-viewer';

interface HighlightPluginProps {
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
  selectedHighlightChunkId?: number | null;
}

interface HighlightOverlayProps {
  highlights: OCRHighlight[];
  pageIndex: number;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
  selectedHighlightChunkId?: number | null;
}

interface HoverToolbarProps {
  // Normalized (0-1) box of the highlight relative to the PDF page
  boxPct: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  onCopy: () => void;
  onMouseEnterToolbar: () => void;
  onMouseLeaveToolbar: () => void;
}

const HoverToolbar: React.FC<HoverToolbarProps> = ({
  boxPct,
  onCopy,
  onMouseEnterToolbar,
  onMouseLeaveToolbar,
}) => {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [copied, setCopied] = useState(false);
  const [coords, setCoords] = useState<{ leftPx: number; topPx: number }>({ leftPx: 0, topPx: 0 });

  useEffect(() => {
    const recalc = () => {
      if (!toolbarRef.current) return;
      const toolbar = toolbarRef.current;
      const toolbarRect = toolbar.getBoundingClientRect();
      const parentEl = toolbar.parentElement as HTMLElement | null;
      const parentRect = parentEl?.getBoundingClientRect();
      if (!parentRect) return;

      // Use normalized values (0-1) to compute px relative to the page layer
      const highlightLeftPx = boxPct.left * parentRect.width;
      const highlightTopPx = boxPct.top * parentRect.height;
      const highlightWidthPx = boxPct.width * parentRect.width;
      const highlightHeightPx = boxPct.height * parentRect.height;

      const gap = 1;
      const spaceBelow = parentRect.height - (highlightTopPx + highlightHeightPx);
      const spaceAbove = highlightTopPx;
      const shouldShowAbove = spaceBelow < toolbarRect.height + gap && spaceAbove >= spaceBelow;
      const desiredPosition: 'below' | 'above' = shouldShowAbove ? 'above' : 'below';

      let topPx =
        desiredPosition === 'below'
          ? highlightTopPx + highlightHeightPx + gap
          : highlightTopPx - toolbarRect.height - gap;

      // Clamp vertically within parent
      topPx = Math.max(2, Math.min(topPx, parentRect.height - toolbarRect.height - 2));

      // Prefer aligning to left edge of highlight. If overflowing right, clamp.
      let leftPx = highlightLeftPx;
      // If enough room to center over the highlight area, center it a bit for nicer look
      const centeredLeft =
        highlightLeftPx + Math.max(0, highlightWidthPx / 2 - toolbarRect.width / 2);
      leftPx = Math.min(centeredLeft, leftPx);

      if (leftPx + toolbarRect.width > parentRect.width - 2) {
        leftPx = parentRect.width - toolbarRect.width - 2;
      }
      leftPx = Math.max(2, leftPx);

      setCoords({ leftPx, topPx });
    };

    recalc();
    window.addEventListener('resize', recalc);
    return () => window.removeEventListener('resize', recalc);
  }, [boxPct, copied]);

  const toolbarStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${coords.leftPx}px`,
    // Minimize the gap and keep within bounds
    top: `${coords.topPx}px`,
    zIndex: 50,
    pointerEvents: 'auto',
  };

  return (
    <div
      ref={toolbarRef}
      style={toolbarStyle}
      onMouseEnter={onMouseEnterToolbar}
      onMouseLeave={onMouseLeaveToolbar}
      className="shadow-lg/60 relative flex items-center gap-0.5 rounded-xl border border-gray-300 bg-white/90 px-0.5 py-0.5 shadow-md backdrop-blur-lg"
    >
      <button
        onClick={() => {
          onCopy();
          setCopied(true);
          window.setTimeout(() => setCopied(false), 2000);
        }}
        disabled={copied}
        className={`flex items-center gap-1 rounded-md px-2 py-1 text-xs transition-colors ${
          copied
            ? 'bg-emerald-50 text-emerald-700'
            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
        }`}
        title={copied ? 'Copied' : 'Copy'}
      >
        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
        <span>{copied ? 'Copied' : 'Copy'}</span>
      </button>

      <div className="mx-1 h-4 w-px bg-gray-200" />

      {/* Action buttons with labels; only Copy is enabled */}
      <button
        disabled
        className="flex cursor-not-allowed items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-400 hover:bg-gray-50"
        title="AI Search (coming soon)"
      >
        <Search className="h-4 w-4" />
        <span>AI Search</span>
      </button>
      <button
        disabled
        className="flex cursor-not-allowed items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-400 hover:bg-gray-50"
        title="Explain (coming soon)"
      >
        <Info className="h-4 w-4" />
        <span>Explain</span>
      </button>
      <button
        disabled
        className="flex cursor-not-allowed items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-400 hover:bg-gray-50"
        title="Translate (coming soon)"
      >
        <Languages className="h-4 w-4" />
        <span>Translate</span>
      </button>

      <button
        disabled
        className="flex cursor-not-allowed items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-400 hover:bg-gray-50"
        title="Ask (coming soon)"
      >
        <MessageCircleQuestion className="h-4 w-4" />
        <span>Ask</span>
      </button>
    </div>
  );
};

const HighlightOverlay: React.FC<HighlightOverlayProps> = ({
  highlights,
  pageIndex,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
  selectedHighlightChunkId,
}) => {
  const pageHighlights = highlights.filter((h) => h.pageIndex === pageIndex);
  const [hoveredHighlightId, setHoveredHighlightId] = useState<string | null>(null);
  const hideTimerRef = useRef<number | null>(null);

  if (pageHighlights.length === 0) {
    return null;
  }

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getHighlightStyle = (highlight: OCRHighlight, isHovered: boolean) => {
    const { boundingBox, confidence, layoutType } = highlight;

    // Convert normalized coordinates to percentages
    const left = `${boundingBox.left * 100}%`;
    const top = `${boundingBox.top * 100}%`;
    const width = `${boundingBox.width * 100}%`;
    const height = `${boundingBox.height * 100}%`;

    // Check if this highlight is the selected one (based on chunkId)
    const isSelectedHighlight =
      selectedHighlightChunkId &&
      highlight.chunkId &&
      (typeof highlight.chunkId === 'string'
        ? parseInt(highlight.chunkId) === selectedHighlightChunkId
        : highlight.chunkId === selectedHighlightChunkId);

    // Only log for selected highlights to reduce noise
    if (isSelectedHighlight) {
      console.log('🟡 SELECTED HIGHLIGHT FOUND on page:', {
        highlightId: highlight.id,
        highlightChunkId: highlight.chunkId,
        selectedHighlightChunkId,
        isSelectedHighlight,
        pageIndex: pageIndex,
        highlightPageIndex: highlight.pageIndex,
        text: `${highlight.text.substring(0, 50)}...`,
      });
    }

    // Color based on confidence - show when hovered OR when it's the selected highlight
    let backgroundColor: string;
    let borderColor: string;

    if (isHovered || isSelectedHighlight) {
      if (isSelectedHighlight) {
        // Special styling for selected highlights - brighter and more prominent
        backgroundColor = 'rgba(59, 130, 246, 0.2)'; // Blue
        borderColor = 'rgba(59, 130, 246, 0.8)';
      } else if (confidence >= 0.9) {
        backgroundColor = 'rgba(34, 197, 94, 0.1)'; // Green
        borderColor = 'rgba(34, 197, 94, 0.5)';
      } else if (confidence >= 0.7) {
        backgroundColor = 'rgba(234, 179, 8, 0.1)'; // Yellow
        borderColor = 'rgba(234, 179, 8, 0.5)';
      } else {
        backgroundColor = 'rgba(239, 68, 68, 0.1)'; // Red
        borderColor = 'rgba(239, 68, 68, 0.5)';
      }

      // Layout type specific adjustments (only for non-selected highlights)
      if (!isSelectedHighlight && (layoutType === 'TITLE' || layoutType === 'LAYOUT_TITLE')) {
        backgroundColor = 'rgba(147, 51, 234, 0.1)'; // Purple for titles
        borderColor = '#7c3aed';
      }
    } else {
      // Transparent when not hovered or selected
      backgroundColor = 'transparent';
      borderColor = 'transparent';
    }

    // Slightly expand the box (1px each side) so the 1px border doesn't overlap text
    const expandedLeft = `calc(${left} - 2px)`;
    const expandedTop = `calc(${top} - 2px)`;
    const expandedWidth = `calc(${width} + 4px)`;
    const expandedHeight = `calc(${height} + 4px)`;

    return {
      position: 'absolute' as const,
      left: expandedLeft,
      top: expandedTop,
      width: expandedWidth,
      height: expandedHeight,
      backgroundColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '2px',
      cursor: enableInteractions ? 'pointer' : 'default',
      pointerEvents: (enableInteractions ? 'auto' : 'none') as React.CSSProperties['pointerEvents'],
      transition: 'all 0.2s ease-in-out',
      zIndex: 10,
    };
  };

  const keepToolbarVisible = () => {
    if (hideTimerRef.current) {
      window.clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
  };

  const scheduleHide = () => {
    if (hideTimerRef.current) {
      window.clearTimeout(hideTimerRef.current);
    }
    hideTimerRef.current = window.setTimeout(() => {
      setHoveredHighlightId(null);
      onHighlightHover?.(null);
      hideTimerRef.current = null;
    }, 200);
  };

  return (
    <div className="pointer-events-none absolute inset-0">
      {pageHighlights.map((highlight) => {
        const isHovered = hoveredHighlightId === highlight.id;
        const isSelectedHighlight =
          selectedHighlightChunkId &&
          highlight.chunkId &&
          (typeof highlight.chunkId === 'string'
            ? parseInt(highlight.chunkId) === selectedHighlightChunkId
            : highlight.chunkId === selectedHighlightChunkId);

        // Log each highlight check for this page (only when we have a selected highlight)
        if (selectedHighlightChunkId && pageHighlights.length > 0) {
          console.log('🟡 Page', pageIndex, 'highlight check:', {
            highlightId: highlight.id,
            highlightChunkId: highlight.chunkId,
            selectedHighlightChunkId,
            isSelectedHighlight,
            text: `${highlight.text.substring(0, 30)}...`,
          });
        }

        const highlightStyle = getHighlightStyle(highlight, isHovered);

        return (
          <React.Fragment key={highlight.id}>
            {/* Highlight Area */}
            <div
              data-highlight-id={highlight.id}
              data-chunk-id={highlight.chunkId}
              style={highlightStyle}
              onClick={() => enableInteractions && onHighlightClick?.(highlight)}
              onMouseEnter={() => {
                if (enableInteractions) {
                  setHoveredHighlightId(highlight.id);
                  onHighlightHover?.(highlight);
                  keepToolbarVisible();
                }
              }}
              onMouseLeave={() => {
                if (enableInteractions) {
                  scheduleHide();
                }
              }}
            />

            {/* Hover Toolbar - Show ONLY for hovered highlights, not selected ones */}
            {isHovered && enableInteractions && (
              <HoverToolbar
                boxPct={{
                  left: highlight.boundingBox.left,
                  top: highlight.boundingBox.top,
                  width: highlight.boundingBox.width,
                  height: highlight.boundingBox.height,
                }}
                onCopy={() => handleCopyText(highlight.text)}
                onMouseEnterToolbar={keepToolbarVisible}
                onMouseLeaveToolbar={scheduleHide}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export const createHighlightPlugin = ({
  highlights,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
  selectedHighlightChunkId,
}: HighlightPluginProps): Plugin => {
  const renderPageLayer = (props: PluginRenderPageLayer) => {
    return (
      <div
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: props.width,
          height: props.height,
          zIndex: 1,
          pointerEvents: 'none',
        }}
      >
        <HighlightOverlay
          highlights={highlights}
          pageIndex={props.pageIndex}
          onHighlightClick={onHighlightClick}
          onHighlightHover={onHighlightHover}
          enableInteractions={enableInteractions}
          selectedHighlightChunkId={selectedHighlightChunkId}
        />
      </div>
    );
  };

  return { renderPageLayer };
};
