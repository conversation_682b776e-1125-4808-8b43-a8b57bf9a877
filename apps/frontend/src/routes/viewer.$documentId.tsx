import { createFileRoute } from '@tanstack/react-router'
import { WorkingPDFViewer } from '../components/pdf/WorkingPDFViewer'
import { documentService } from '../services/documentService'

export const Route = createFileRoute('/viewer/$documentId')({
  component: PDFViewerPage,
})

function PDFViewerPage() {
  const { documentId } = Route.useParams()

  return (
    <div className="h-screen bg-slate-900 flex flex-col">
      <div className="bg-slate-800 px-4 py-3 flex items-center justify-between border-b border-slate-700">
        <div className="flex items-center space-x-4">
          <button
            type="button"
            onClick={() => window.history.back()}
            className="text-slate-200 hover:text-white transition-colors"
          >
            ← Back
          </button>
          <h1 className="text-white font-medium">PDF Viewer with OCR</h1>
        </div>
        <div className="text-slate-400 text-sm">
          Document ID: {documentId}
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden bg-white">
        <WorkingPDFViewer
          documentId={documentId}
          documentUrl={documentService.getDocumentUrl(documentId)}
          showOcrOverlay={true}
          showControls={true}
          showChunks={true}
          className="h-full"
        />
      </div>
    </div>
  )
}
